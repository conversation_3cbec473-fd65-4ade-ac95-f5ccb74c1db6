<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Session\SessionInterface;

/**
 * Inventory Controller
 *
 * Handles inventory management functionality
 *
 * @copyright 2009 SpareFoot Inc
 * <AUTHOR> Agent
 */
class InventoryController extends AbstractRestrictedController
{
   

    /**
     * Initialize controller
     */
    protected function _init(): void
    {
        // Initialize inventory-specific data if needed
        $this->view->title = 'Inventory Management';
    }

    /**
     * Inventory Index Action - Main inventory management page
     */
    #[Route('/inventory', name: 'inventory_index', methods: ['GET'])]
    public function index(Request $request): Response
    {
        $user = $this->getUser();
        // Initialize the view data
        $this->view->account = $this->getLoggedUser()->getAccount();

        if ($this->getSession()->get('facilityId')) {
            $facility = \Genesis_Service_Facility::loadById($this->getSession()->get('facilityId'));
            $this->view->facility = $facility;
        }

        // Add any inventory-specific scripts and styles
        $this->view->scripts = [
            'facility/global-functions',
            'inventory/index'
        ];

        $this->view->title = 'Inventory Management';

        return $this->render('inventory/index.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Get the current tab for navigation
     */
    protected function getTab(): string
    {
        return 'inventory'; // You may need to define this constant in the parent class
    }
}
