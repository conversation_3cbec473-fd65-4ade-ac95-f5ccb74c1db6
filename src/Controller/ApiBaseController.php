<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\Facility;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\Util;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 *   // Batch of data
 *   status: 500
 *   errors: [
 *      code: 'SERVER_FAILURE'
 *      id: 100024
 *      title: 'Server has failed'
 *   ],
 *   facilities:
 *     data: [{}, {}, {}],
 *     meta:
 *       totalRecords: 0,
 *       limit: 0,
 *       offset: 0
 *   units:
 *     data: [],
 *     meta:
 *       totalRecords: 0,
 *       limit: 0,
 *       offset: 0.
 *
 *   // Single record
 *   status: 500
 *   errors: [
 *      code: 'SERVER_FAILURE'
 *      id: 100024
 *      title: 'Server has failed'
 *   ],
 *   data: {}
 *   meta:
 *     totalRecords: 0,
 *     limit: 0,
 *     offset: 0
 *
 *   // Array of records but ONLY single resource
 *   status: 500
 *   errors: [
 *      code: 'SERVER_FAILURE'
 *      id: 100024
 *      title: 'Server has failed'
 *   ],
 *   data: [{}, {}, {}]
 *   meta:
 *     totalRecords: 0,
 *     limit: 0,
 *     offset: 0
 **/
abstract class ApiBaseController extends AbstractController
{
    /**
     * Initialize API controller for Symfony
     * In Symfony, this should be called at the beginning of each action method.
     *
     * @throws ApiException
     */
    protected function initApi(Request $request, bool $requireAuth = true): void
    {
        // In Symfony, we don't need to disable layout or view renderer
        // as we'll return JsonResponse objects directly

        // Set custom exception handler for API responses
        // Note: In Symfony, this is typically handled by exception listeners/subscribers
        set_exception_handler([ApiException::class, 'apiExceptionHandler']);

        if (!$requireAuth) {
            return;
        }

        // Check authentication
        $user = User::getLoggedUser();
        $controllerName = $request->attributes->get('_controller');

        if (!$user && !str_contains($controllerName, 'api-login')) {
            throw new ApiException(ApiException::UNAUTHORIZED, 'you are not signed in');
        }
    }

    /**
     * @return \Genesis_Service_UserAccess|false
     */
    protected function getLoggedUser()
    {
        // special for api, bypasses setting fac and accountId
        return UserOauth::getUserAccess();
    }

    protected static function sendOKEmptyResponse(): Response
    {
        return new Response('', 204);
    }

    public function validateAndGetFacility($facilityId, $checkWriteAccess = false)
    {
        try {
            $facility = Facility::validateFacilityId($facilityId);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }
        try {
            User::validateFacilityAccess($facility, $checkWriteAccess);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }

        return $facility;
    }

    public function validateAndGetUser($userId, $action = null)
    {
        try {
            $user = User::validateUserId($userId);
            User::validateUserAccess($user, $action);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED);
        }

        return $user;
    }

    public function validateIsoDate($date, $paramName)
    {
        if (!Util::isValidIsoDate($date)) {
            throw new ApiException(ApiException::NOT_ACCEPTABLE, 'Invalid ISO date for parameter '.$paramName);
        }
    }

    public function validateStartEndDate($startDate, $endDate)
    {
        if ($endDate !== null) {
            $this->validateIsoDate($endDate, 'endDate');
        }
        if ($startDate !== null) {
            $this->validateIsoDate($startDate, 'startDate');
        }
        if (strtotime($startDate) > strtotime($endDate) && ($endDate !== null && $startDate != null)) {
            throw new ApiException(ApiException::BAD_REQUEST, 'startDate must by earlier than endDate');
        }
    }
}
