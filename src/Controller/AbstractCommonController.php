<?php

namespace Sparefoot\MyFootService\Controller;

use Spa<PERSON>foot\MyFootService\Controller\AbstractView\GenericView;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;

/**
 * Abstract Common Controller
 * 
 * Base controller containing shared functionality for both public and restricted controllers
 *
 * @copyright 2009 Sparefoot Inc
 * <AUTHOR>
 * Migrated to Symfony by Augment Agent
 */
abstract class AbstractCommonController extends AbstractController
{
    protected $view;
    protected $request;

    /**
     * Initialize controller - override in child controllers
     * @param Request $request The request object (available for child controllers)
     */
    protected function _init(): void
    {
        // Override in child controllers
    }

    /**
     * Get the current tab for navigation - override in child controllers
     */
    protected function getTab(): string
    {
        return '';
    }

    /**
     * Dispatch error messages
     */
    protected function dispatchError($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->errorMessages)) {
            $this->view->errorMessages[] = $messages;
        } else {
            $this->view->errorMessages = array($messages);
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('error', $messages);
    }

    /**
     * Dispatch success messages
     */
    protected function dispatchSuccess($messages): void
    {
        // In Symfony, we would typically use flash messages
        // For backward compatibility, we'll add to the view object
        if (is_array($this->view->successMessages)) {
            $this->view->successMessages[] = $messages;
        } else {
            $this->view->successMessages = array($messages);
        }

        // TODO: Consider using Symfony flash messages:
        // $this->addFlash('success', $messages);
    }

    /**
     * We need to sanitize the params to prevent xss attacks
     * In Symfony, this is typically handled by the Request object and form validation
     *
     * @param Request $request
     * @param string $paramName
     * @param mixed $default
     * @return mixed
     */
    public function getParam(Request $request, string $paramName, $default = null)
    {
        $param = $request->get($paramName, $default);
        return is_array($param) ? $param : htmlspecialchars($param, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Helper method to extract controller name from Symfony route
     */
    protected function getControllerNameFromRoute(?string $route): string
    {
        if (!$route) {
            return '';
        }

        // Extract controller name from route pattern
        // This is a simplified approach - you may need to adjust based on your routing patterns
        $parts = explode('_', $route);
        return $parts[0] ?? '';
    }

    /**
     * Set the request object
     */
    public function setRequest(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Get the request object
     */
    public function getRequest(): Request
    {
        return $this->request;
    }

    protected function getControllerActionName(Request $request): string
    {
        $controller = $request->attributes->get('_controller');
        /*
            Example: $parts = ^ array:3 [▼
                    0 => "Sparefoot\PitaService\Controller\FacilityController"
                    1 => ""
                    2 => "indexAction"
            ]
            */
        $parts = explode(':', $controller);

        $actionName = end($parts);
        $actionName = str_replace('Action', '', $actionName);

        return $actionName;
    }
}
