<?php

namespace Sparefoot\MyFootService\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class PingController extends AbstractController
{
    /**
     * Sample:
     * https://myfoot.sparefoot.com/ping
     * http://localhost:9019/ping
     */
    #[Route('/ping{slash}', name: 'ping_index', requirements: ['slash' => '/?'], defaults: ['slash' => ''])]
    public function indexAction(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $session = $request->getSession();

        $response = [
            'sessionId' => $session ? $session->getId() : null,
            'user' => $user ? $user->getUserIdentifier() : null,
            'statusInstall' => 'successful',
            'name' => 'MyFoot',
            'version' => getenv('VERSION'),
            'sf_env' => getenv('SF_ENV'),
            'hostname' => gethostname(),
            'response' => 'pong - myfoot',
        ];

        return new JsonResponse($response);
    }
}
