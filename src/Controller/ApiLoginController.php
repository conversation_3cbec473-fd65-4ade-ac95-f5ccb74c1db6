<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\Authorization\SDK\Token;
use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Service\UserCookie;
use Sparefoot\MyFootService\Service\UserOauth;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class ApiLoginController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login
     * http://localhost:9019/api/login
     */
    public function indexAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        if (!$request->isMethod('POST') && !$request->isMethod('GET')) {
            throw new ApiException(ApiException::NOT_IMPLEMENTED);
        }
        if (!$request->get('email')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Email is required');
        }
        if (!$request->get('password')) {
            throw new ApiException(ApiException::BAD_REQUEST, 'Password is required');
        }
        try {
            UserOauth::authenticate(
                $request->get('email'),
                $request->get('password')
            );

            return $this->json(['success' => true]);
        } catch (\Exception $e) {
            throw new ApiException(ApiException::UNAUTHORIZED, $e->getMessage());
        }
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login/logout
     * http://localhost:9019/api/login/logout
     */
    public function logoutAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        UserOauth::logout();

        return $this->json(['success' => true]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login/renew
     * http://localhost:9019/api/login/renew
     */
    public function renewAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        try {
            UserOauth::renew();
        } catch (\Exception $e) {
            throw new ApiException(ApiException::BAD_REQUEST, $e->getMessage());
        }

        return $this->json(['success' => true]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login/token
     * http://localhost:9019/api/login/token
     */
    public function tokenAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $t = UserOauth::getToken();
        if (!$t) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'no token exists, maybe you need to sign in?');
        }
        $remainingSeconds = $t->getExpires() - time();
        $days = floor($remainingSeconds / (60 * 60 * 24));
        $remainingSeconds = $remainingSeconds % (60 * 60 * 24);
        $hours = floor($remainingSeconds / (60 * 60));
        $remainingSeconds = $remainingSeconds % (60 * 60);
        $minutes = floor($remainingSeconds / 60);
        $seconds = $remainingSeconds % 60;
        $response = [
            'data' => [
                'token' => [
                    'identifier' => $t->getIdentifier(),
                    'domain' => $t->getDomain(),
                    'host' => $t->getHost(),
                    'expires' => $t->getExpires(),
                ],
                'meta' => [
                    'human_expires' => date('Y-m-d H:i:s', $t->getExpires()),
                    'needs_renew' => UserOauth::needsRenew(),
                    'ttl' => [
                        'days' => $days,
                        'minutes' => $minutes,
                        'hours' => $hours,
                        'seconds' => $seconds,
                    ],
                ],
            ],
        ];

        return $this->json($response, 200, [], ['json_encode_options' => JSON_PRETTY_PRINT]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login/user
     * http://localhost:9019/api/login/user
     */
    public function userAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $user = UserOauth::getUser();
        if (!$user) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'must be signed in');
        }
        $response = [
            'data' => [
                'email' => $user->getEmail(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'account_id' => $user->getAccountId(),
                'myfoot_role' => $user->getMyfootRole(),
                'user_id' => $user->getUserId(),
                'restricted_to_facility_ids' => $user->getRestrictedToFacilityIds(),
            ],
        ];

        return $this->json($response, 200, [], ['json_encode_options' => JSON_PRETTY_PRINT]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login/cookie
     * http://localhost:9019/api/login/cookie
     */
    public function cookieAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        $cookieClass = new UserCookie();
        $reflection = new \ReflectionClass($cookieClass);
        $data = [];
        foreach ($reflection->getConstants() as $constantValue) {
            $data[$constantValue] = UserCookie::get($constantValue, 'not set');
        }

        return $this->json(['data' => $data]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/api/login/prenew
     * http://localhost:9019/api/login/prenew
     */
    public function prenewAction(Request $request): JsonResponse
    {
        $this->initApi($request, false);

        // will return true if the user needs to renew, false if they don't
        $user = UserOauth::getUser();
        if (!$user) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'must be signed in');
        }

        $rawtoken = UserCookie::get(UserOauth::AUTH_BEARER_TOKEN);
        if (!$rawtoken) {
            throw new ApiException(ApiException::PRECONDITION_FAILED, 'no cookie set');
        }
        $token = Token::createFromRaw($rawtoken);

        UserCookie::set(UserOauth::AUTH_BEARER_TOKEN, $token->__toString());

        return $this->json(['success' => 1]);
    }
}
