<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\Account;
use Sparefoot\MyFootService\Service\Facility;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Account Controller
 *
 * @copyright 2025 SpareFoot Inc
 * <AUTHOR> Copilot
 */
class AccountController extends AbstractRestrictedController
{
    /**
     * Initialize controller
     */
    protected function _init(): void
    {
        // Account-specific initialization can go here
    }

    /**
     * Account Overview Action
     */
    #[Route('/account/overview', name: 'account_overview', methods: ['GET'])]
    public function overviewAction(Request $request): Response
    {
        // Get account ID from request parameter or session
        $accountId = $request->get('account_id');
        if (!$accountId) {
            $accountId = $this->getSession($request)->get('accountId');
        }

        if (!$accountId) {
            throw new \Exception('No account ID provided');
        }

        // Load account and related data
        $account = \Genesis_Service_Account::loadById($accountId);
        if (!$account) {
            throw new \Exception('Account not found');
        }

        // Check if user has permission to view this account
        if (!$this->getLoggedUser()->isMyfootGod() && !$this->getLoggedUser()->isMyfootAdmin()) {
            if ($this->getLoggedUser()->getAccountId() != $accountId) {
                throw new \Exception('Access denied');
            }
        }

        // Set view data
        $this->view->account = $account;
        $this->view->accountId = $accountId;
        
        // Get account facilities
        $facilities = $account->getFacilities();
        $this->view->facilities = $facilities;
        
        // Get account statistics or other overview data
        $this->view->stats = [
            'totalFacilities' => count($facilities),
            'accountStatus' => $account->getStatus(),
            'bidType' => $account->getBidType(),
        ];

        $this->view->title = 'Account Overview';

        return $this->render('account/overview.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Get the current tab for navigation
     */
    protected function getTab(): string
    {
        return parent::TAB_HOME;
    }
}
