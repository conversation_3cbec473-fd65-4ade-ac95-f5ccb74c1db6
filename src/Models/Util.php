<?php

namespace Sparefoot\MyFootService\Models;

use Sparefoot\MyFootService\Service\User;

class Util
{
    public static function getMyFootLandingPage()
    {
        $result = '/';

        // Specific facility?
        $session = User::getSession();

        // Determine page
        $userAccess = User::getLoggedUser();
        if ($userAccess->isMyfootAdmin() && count($userAccess->getManageableFacilityIds()) > 1) {
            $result .= 'overview';
            if ($session->get('accountId')) {
                $result .= '?account_id='.$session->get('accountId');
            }
        } else {
            $result .= 'dashboard';
            if ($session->get('facilityId')) {
                $result .= '?fid='.$session->get('facilityId');
            }
        }

        return $result;
    }
}
