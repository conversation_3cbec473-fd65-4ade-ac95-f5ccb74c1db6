<?php

namespace Sparefoot\MyFootService\Service;

use Sparefoot\Authorization\SDK\AuthorizationException;
use Sparefoot\Authorization\SDK\Client;
use Sparefoot\Authorization\SDK\Token;
use Sparefoot\MyFootService\Models\Features;

class UserOauth extends AbstractService
{
    public const AUTH_BEARER_TOKEN = UserCookie::AUTH_BEARER_TOKEN;
    public const CABINET_AUTH_REFRESH_TOKEN = 'authRenewToken';
    /**
     * @var Sparefoot\Authorization\SDK\User
     */
    private static $_authedUser; // faster than cookie, avail on same request

    /**
     * @throws Exception
     */
    public static function authenticate($email, $password)
    {
        try {
            $token = Client::getInstance()->getAuthTokenFromUserCredentials($email, $password);
            self::_setNewTokenServices($token);
        } catch (\Exception $e) {
            if (\Genesis_Service_Feature::isActive(Features::LOG_AUTH_ERROR)) {
                // Genesis_Util_ErrorLogger::exceptionToHipChat($e);
                \Genesis_Util_Logger::log('Myfoot Auth Error', $e->getMessage(), $e->getFile(), $e->getLine(), \Genesis_Util_Logger::ERR_LEVEL_ERR);
            }
            throw new \Exception('Authentication failed: '.$e->getMessage());
        }
    }

    public static function renew()
    {
        if (!self::getUser()) {
            throw new \Exception('user is required');
        }
        self::_renew();
    }

    private static function _renew()
    {
        // try and refresh the token
        try {
            $cabinet = \Genesis_Service_Cabinet::get();
            if (!$cabinet->getMeta(self::CABINET_AUTH_REFRESH_TOKEN)) {
                throw new \Exception('refresh cabinet key is not set');
            }
            $token = Client::getInstance()->refreshAuthToken($cabinet->getMeta(self::CABINET_AUTH_REFRESH_TOKEN));
            if (!$token) {
                throw new \Exception('no renew token found for client, cannot renew');
            }
            self::_setNewTokenServices($token);
        } catch (\Exception $e) {
            if (\Genesis_Service_Feature::isActive(Features::LOG_AUTH_ERROR)) {
                // Genesis_Util_ErrorLogger::exceptionToHipChat($e);
                \Genesis_Util_Logger::log('Myfoot Auth Error', $e->getMessage(), $e->getFile(), $e->getLine(), \Genesis_Util_Logger::ERR_LEVEL_ERR);
            }
            throw new \Exception('refresh auth token failed: '.$e->getMessage());
        }
    }

    private static function _setNewTokenServices(Token $token)
    {
        // set the cookie for js and ident
        self::_setAuthTokenCookie($token);
        // set the renew into cabinet
        $cabinet = \Genesis_Service_Cabinet::get();
        $cabinet->setMeta(self::CABINET_AUTH_REFRESH_TOKEN, $token->getRefresh());
        \Genesis_Service_Cabinet::save($cabinet);
    }

    /**
     * @return \Sparefoot\Authorization\SDK\User|bool|false
     *
     * @throws Exception
     */
    public static function getUser()
    {
        if (self::$_authedUser === null) {
            $currentRawToken = UserOauth::_getAuthToken();

            if ($currentRawToken === null) {
                return self::$_authedUser = false;
            }
            try {
                $authUser = Client::getInstance()->getUserFromAuthToken($currentRawToken);
            } catch (AuthorizationException $e) {
                return self::$_authedUser = false;
            }

            if (!$authUser) {
                return self::$_authedUser = false;
            }

            // renew automatic if we need it
            try {
                if (self::_needsRenew()) {
                    self::_renew();
                }
            } catch (\Exception $e) {
                return self::$_authedUser = false;
            }

            self::$_authedUser = $authUser;
        }

        return self::$_authedUser;
    }

    public static function getUserAccess()
    {
        static $userAccess = null;
        if ($userAccess === null) {
            if (!self::getUser()) {
                $userAccess = false;
            } else {
                $userAccess = \Genesis_Service_UserAccess::loadById(self::getUser()->getUserId());
            }
        }

        return $userAccess;
    }

    public static function logout()
    {
        self::_unsetAuthTokenCookie();
        $cabinet = \Genesis_Service_Cabinet::get();
        $cabinet->setMeta(self::CABINET_AUTH_REFRESH_TOKEN, false);
        \Genesis_Service_Cabinet::save($cabinet);
        self::$_authedUser = false;
    }

    private static function _setAuthTokenCookie(Token $token)
    {
        UserCookie::set(self::AUTH_BEARER_TOKEN, $token->__toString());
    }

    private static function _unsetAuthTokenCookie()
    {
        UserCookie::clear(self::AUTH_BEARER_TOKEN);
    }

    private static function _getAuthTokenCookie()
    {
        return UserCookie::get(self::AUTH_BEARER_TOKEN);
    }

    private static function _getAuthTokenHeader()
    {
        $request = self::getRequest();
        if ($request) {
            return $request->headers->get('Authorization');
        }
        
        return \Zend_Controller_Front::getInstance()->getRequest()->getHeader('Authorization');
    }

    private static function _getAuthToken()
    {
        // Get token from Header
        $token = self::_getAuthTokenHeader();

        // If no token, get token from Cookie
        $token = $token ? $token : self::_getAuthTokenCookie();

        return $token;
    }

    public static function needsRenew()
    {
        if (!self::getUser()) {
            throw new \Exception('user required');
        }

        return self::_needsRenew();
    }

    private static function _needsRenew()
    {
        $token = self::getToken();
        if (!$token) {
            throw new \Exception('no token found to decode');
        }

        return (strtotime('+12 days') > $token->getExpires()) ? true : false;
    }

    public static function getToken()
    {
        $currentRawToken = self::_getAuthToken();
        if (!$currentRawToken) {
            return false;
        }

        return Token::createFromRaw($currentRawToken);
    }
}
