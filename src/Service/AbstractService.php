<?php

namespace Sparefoot\MyFootService\Service;

use Symfony\Component\HttpFoundation\Request;

abstract class AbstractService
{
    protected static ?Request $request = null;

    /**
     * Set the current request
     *
     * @param Request $request
     */
    public static function setRequest(Request $request): void
    {
        self::$request = $request;
    }

    /**
     * Get the current request
     *
     * @return Request|null
     */
    protected static function getRequest(): ?Request
    {
        return self::$request;
    }
}
