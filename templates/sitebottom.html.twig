<script type="text/javascript" src="<?=CDN('/dist/app.js')?>"></script>

<script>
    // Bootstrap - No conflicts cleanup
    $.fn.bDropdown = $.fn.dropdown.noConflict();
    $.fn.bModal = $.fn.modal.noConflict();
</script>

<script src="<?=CDN('/new-ui/dist/myfoot.js')?>"></script>

<!--[if lte IE 9]>
<script src="<?=CDN('/new-ui/js/ie-fixes.js')?>"></script>
<![endif]-->

<?=$this->inlineScript()?>

<?php if ($this->scripts): ?>
    <?php foreach($this->scripts as $script): ?>
        <script type="text/javascript" src="<?=CDN('/js/'.$script.'.js')?>"></script>
    <?php endforeach; ?>
<?php endif; ?>

<?php if ($this->loggedUser): ?>
    <script type="text/javascript">
        $(function(){

            function getLoggedInState(){
                return new window.Promise(function(resolve, reject){
                    return $.ajax({
                        url: '/api/me',
                        json: true
                    })
                    .then(resolve, function(err){
                        if(err && err.status === 401){
                            return reject(new Error('permission_denied'));
                        }
                        // something else may have happened, but we dont really care
                        // as we're looking for authorization errors only
                        return resolve();
                    });
                });
            }

            function checkLoggedInState(){
                return getLoggedInState()
                .catch(function(){
                    location.href = '/login';
                });
            }

            var FIVE_MINUTES = 60 * 5 * 1000;
            var loggedInStateTimeout;
            function loggedInStateTick(immediate){
                clearTimeout(loggedInStateTimeout);

                loggedInStateTimeout = setTimeout(function(){
                    checkLoggedInState()
                    .then(function(){
                        loggedInStateTick();
                    });
                }, immediate ? 0 : FIVE_MINUTES);
            }

            $(window).focus(function(){
                loggedInStateTick(true);
            });

            $(window).blur(function(){
                clearTimeout(loggedInStateTimeout);
            });

            loggedInStateTick();
        });
    </script>
<?php endif; ?>

<script>
    // Rollbar jQuery Snippet
    !function(r){function t(n){if(e[n])return e[n].exports;var a=e[n]={exports:{},id:n,loaded:!1};return r[n].call(a.exports,a,a.exports,t),a.loaded=!0,a.exports}var e={};return t.m=r,t.c=e,t.p="",t(0)}([function(r,t,e){"use strict";!function(r,t,e){var n=t.Rollbar;if(n){var a="0.0.8";n.configure({notifier:{plugins:{jquery:{version:a}}}});var o=function(r){if(n.error(r),t.console){var e="[reported to Rollbar]";n.options&&!n.options.enabled&&(e="[Rollbar not enabled]"),t.console.log(r.message+" "+e)}};r(e).ajaxError(function(r,t,e,a){var o=t.status,u=e.url,i=e.type;if(o){var s={status:o,url:u,type:i,isAjax:!0,data:e.data,jqXHR_responseText:t.responseText,jqXHR_statusText:t.statusText},d=a?a:"jQuery ajax error for "+i;n.warning(d,s)}});var u=r.fn.ready;r.fn.ready=function(r){return u.call(this,function(t){try{r(t)}catch(e){o(e)}})};var i=r.event.add;r.event.add=function(t,e,n,a,u){var s,d=function(r){return function(){try{return r.apply(this,arguments)}catch(t){o(t)}}};return n.handler?(s=n.handler,n.handler=d(n.handler)):(s=n,n=d(n)),s.guid?n.guid=s.guid:n.guid=s.guid=r.guid++,i.call(this,t,e,n,a,u)}}}(jQuery,window,document)}]);
    // End Rollbar jQuery Snippet
</script>
<script async src='//www.google-analytics.com/analytics.js'></script>
<script>
    window.ga=window.ga||function(){(ga.q=ga.q||[]).push(arguments)};ga.l=+new Date;
    ga('create', '<?=Genesis_Config_Server::isProduction() ? '**********-14' : '**********-19' ?>', 'auto');
    <?php if (!$this->excludeInitialPageView): ?>
    ga('send', 'pageview');
    <?php endif; ?>
    ga('set', '&uid', '<?=$this->userId?>');
</script>

<?php if (Genesis_Config_Server::isProduction()) : ?>
    <?php if (Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::SALESFORCE_PARDOT)): ?>
    <script type="text/javascript">
        piAId = '16382';
        piCId = '2871';

        (function() {
            function async_load(){
                var s = document.createElement('script'); s.type = 'text/javascript';
                s.src = ('https:' == document.location.protocol ? 'https://pi' : 'http://cdn') + '.pardot.com/pd.js';
                var c = document.getElementsByTagName('script')[0]; c.parentNode.insertBefore(s, c);
            }
            if(window.attachEvent) { window.attachEvent('onload', async_load); }
            else { window.addEventListener('load', async_load, false); }
        })();
    </script>
    <?php endif; ?>
    <?php if (!$this->excludeQualaroo && Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::QUALAROO_HOOK)): ?>
    <script type="text/javascript" charset="utf-8">
        var _kiq = _kiq || [];
        (function(){
            setTimeout(function(){
                var d = document, f = d.getElementsByTagName('script')[0], s = d.createElement('script'); s.type = 'text/javascript';
                s.async = true; s.src = '//s3.amazonaws.com/ki.js/53735/dp9.js'; f.parentNode.insertBefore(s, f);
            }, 1);
        })();
        _kiq.push(['identify', '<?=$this->userId?>']);
    </script>
    <?php endif; ?>
    <?php if (Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::SESSION_CAM_RECORDER)): ?>
    <!-- SessionCam Client Integration v6.0 -->
    <script type="text/javascript">
        //<![CDATA[
        var scRec=document.createElement('SCRIPT');
        scRec.type='text/javascript';
        scRec.src="//d2oh4tlt9mrke9.cloudfront.net/Record/js/sessioncam.recorder.js";
        document.getElementsByTagName('head')[0].appendChild(scRec);
        ////]]>
    </script>
    <!-- End SessionCam -->
    <?php endif; ?>
<?php endif;?>