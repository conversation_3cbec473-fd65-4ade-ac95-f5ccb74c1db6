{# Dashboard Index Template - Migrated from application/views/scripts/dashboard/index.phtml #}

{% extends 'layout.html.twig' %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}
<script type="text/javascript">
    App.context = {
        facility_id: {{ view.facility ? view.facility.getId() : 'null' }}
    };
</script>

{% if view.facility is defined and view.facility is not empty %}
    {% if not view.facility.getActive() %}
        <div id="inactive-facility-message" class="ui warning message" style="overflow:auto">
            <div class="header">
                This facility is currently inactive and will not receive reservations on the SpareFoot network.
                <button id="activate-facility" class="ui button compact primary pull-right">Activate Facility</button>
            </div>
        </div>
    {% endif %}
{% endif %}

<div class="ui stackable two column grid">
    <div class="column">
        <div class="ui segment dashboard-widget welcome-widget {{ view.showWelcomeWidget ? '' : 'hidden' }}">
            <div class="head">
                <h2 class="widget-title">Welcome to SpareFoot!</h2>
                <p>
                    Adding photos, requesting reviews, and adding additional unit types will help drive customers to your facility listing and improve conversion rates.
                </p>
            </div>
            <div class="body">
                {# TODO: Replace with proper Symfony URL generation #}
                <a href="/features/photos?fid={{ view.facility ? view.facility.getId() : '' }}" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="photo icon"></i>
                        <h3>Add Photos</h3>
                    </div>
                </a>
                <a href="/reviews/request?fid={{ view.facility ? view.facility.getId() : '' }}" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="star icon"></i>
                        <h3>Request Reviews</h3>
                    </div>
                </a>
                <a href="/features/units?fid={{ view.facility ? view.facility.getId() : '' }}" class="icon-square">
                    <div class="icon-square-inner">
                        <i class="building outline icon"></i>
                        <h3>Add Unit Types</h3>
                    </div>
                </a>
            </div>
        </div>
        
        {# TODO: Implement feature checking in Twig - for now using view variables #}
        {% if view.showSubmitRateWidget is defined and view.showSubmitRateWidget %}
        <div class="ui segment dashboard-widget submit-rate-widget"></div>
        {% endif %}

        {% if view.showMoveInRateWidget is defined and view.showMoveInRateWidget %}
        <div class="ui segment dashboard-widget move-in-rate-chart {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}
    </div>
    <div class="column">
        {% if view.showInventoryWidget is defined and view.showInventoryWidget %}
        <div class="ui segment dashboard-widget inventory-widget {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}

        {% if view.showCurrentBidWidget is defined and view.showCurrentBidWidget and view.account.getBidType() != constant('Genesis_Entity_Account::BID_TYPE_RESIDUAL') %}
        <div class="ui segment dashboard-widget current-bid-widget {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}

        {% if view.showCustomerReviewsWidget is defined and view.showCustomerReviewsWidget %}
        <div class="ui segment dashboard-widget customer-reviews-widget {{ view.showWelcomeWidget ? 'hidden' : '' }}"></div>
        {% endif %}
    </div>
</div>

<div class="bottom-more-row">
    {# TODO: Replace with proper Symfony URL generation #}
    <a href="/reports">View More Reports &gt;</a>
</div>

<div class="clear"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {# Include dashboard-specific scripts #}
    {% for script in view.scripts %}
        <script src="{{ asset('js/' ~ script ~ '.js') }}"></script>
    {% endfor %}
{% endblock %}
