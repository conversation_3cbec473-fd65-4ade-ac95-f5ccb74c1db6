<?php
$fid = $this->session->facilityId;
$accountId = $this->session->accountId;

$facility = Genesis_Service_Facility::loadById($fid);
$account = Genesis_Service_Account::loadById($accountId);
$hasOmiCapableFms = AccountMgmt_Service_User::hasAccessToOmiCapableFms();

if (!empty($fid)) {
    $pendo = new AccountMgmt_Models_PendoData(AccountMgmt_Service_User::getLoggedUser(), $fid);
    $pendoData = $pendo->buildPendoObject();
    echo $this->partial('pendoscript.phtml', ["pendoData" => $pendoData]);
}

//if we don't have a facility, we are not Full Service yet
$isFullService = $facility ?  $facility->getType() == \Genesis_Entity_Facility::TYPE_VALET : 0;

$featuresArray = ['features', 'features-units', 'features-listings', 'features-bid', 'features-bid-custom'];
$moveinsArray = ['move-ins', 'contactless-move-ins', 'online-move-ins'];

{% set route = view.routeName %}
{% set action = view.actionName %}
{% set fid = view.facilityId %}
{% set accountId = view.accountId %}
{% set loggedUser = view.loggedUser %}
?>

<div id="site-sidebar" class="ui visible overlay labeled left vertical sidebar menu sidebar-nav">
    <div class="logo">
        <a href="<?=$this->url([], 'dashboard')?>">
            <img src="/images/MSFLogo.png" alt="mysparefootlogo"/>
        </a>
    </div>

    {# only god users can see the account picker #}
    {% if loggedUser is defined and loggedUser.isMyfootGod() %}
        <div class="item accounts-dropdown-container hidden">
            <div id="accounts-dropdown" class="ui dropdown search fluid"></div>
        </div>

        <div id="accounts-search" class="ui search item">
            <div class="ui icon input">
                <i class="search icon"></i>
                <input class="prompt hidden" type="text" placeholder="Accounts"/>
                <div class="selected"></div>
            </div>
            <div class="results"></div>
        </div>
    {% endif %}

    {# if is admin and there are managable facilities #}
    {% if loggedUser is defined and loggedUser.isMyfootAdmin() and loggedUser.getManageableFacilityIds()|length > 1 %}
    <a class="item {{ route == 'account_overview' ? 'active yellow' : '' }}" data-page="overview" data-segment-category="sidebar" data-segment-label="overview" id="menu-overview" href="{{ url('account_overview') }}?account_id={{ accountId }}">
        <i class="world icon"></i> Overview
    </a>
    {% endif %}

    <a class="item {{ route == 'dashboard_index' ? 'active yellow' : '' }}" data-page="dashboard" data-segment-category="sidebar" data-segment-label="dashboard" id="menu-dashboard" href="{{ url('dashboard_index') }}?fid={{ fid }}">
        <i class="dashboard icon"></i> Dashboard
    </a>

    <?php if ($this->loggedUser->getAccount()->getCpa()): ?>
        <?php if(Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::BID_OPTIMIZER, ["account_id"=>$this->loggedUser->getAccountId()])): ?>
            <a class="item <?= $route === 'features-demand-optimizer' ? 'active yellow' : ''?>" data-page="bidding" data-segment-category="sidebar" data-segment-label="bidding" id="menu-bidding" href="<?=$this->url(['action'=>'demandoptimizer'], 'features')?>?fid=<?=$fid?>">
                <i class="chart line icon"></i> Bidding
            </a>
        <?php else: ?>
            <a class="item <?= in_array($route, ['bidding', 'features-bid', 'features-bid-custom']) || $action === 'bulkbid' ? 'active yellow' : ''?>" data-page="bidding" data-segment-category="sidebar" data-segment-label="bidding" id="menu-bidding" href="<?=$this->url(['action'=>'bid'], 'features')?>?fid=<?=$fid?>">
                <i class="chart line icon"></i> Bidding
            </a>
        <?endif; ?>
        <?php $featuresArray = array_diff($featuresArray, ['features-bid', 'features-bid-custom']);?>
    <?php endif; ?>

    <a class="item <?= $route === 'customers' ? 'active yellow' : ''?>" data-page="customers" data-segment-category="sidebar" data-segment-label="customers" id="menu-customers" href="<?=$this->url(['action'=>'reservations'], 'customers')?>?fid=<?=$fid?>">
        <i class="users icon"></i> Customers
    </a>

    <a class="item <?= in_array($route, $featuresArray) && $action != 'bulkbid' ? 'active yellow' : ''?>"
        data-page="features" data-segment-category="sidebar" data-segment-label="features" id="menu-features" href="<?=$this->url(['action'=>'units'], 'features')?>?fid=<?=$fid?>">
        <i class="building outline icon"></i> Features
    </a>

    <a class="item <?= in_array($route, ['reviews', 'reviews-one', 'reviews-request']) ? 'active yellow' : ''?>" data-page="reviews" data-segment-category="sidebar" data-segment-label="reviews" id="menu-reviews" href="<?=$this->url(['action'=>'index'], 'reviews')?>?fid=<?=$fid?>">
        <i class="star icon"></i> Reviews
    </a>

    <?php // if billing is enabled
    if(isset($this->loggedUser) && $this->loggedUser->canUseBilling()): ?>
    <a class="item <?= $route === 'statement' && !in_array($action, ['dispute', 'view']) ? 'active yellow' : ''?>" data-page="statements" data-segment-category="sidebar" data-segment-label="statements" id="menu-statements" href="<?=$this->url(['action' => 'list'], 'statement')?>?account_id=<?=$accountId?>">
        <i class="list icon"></i> Statements
    </a>
    <?php endif; ?>

    <?php
        if (isset($this->loggedUser) && $this->loggedUser->canUseBilling()):
            $openStatementBatch = Genesis_Service_StatementBatch::loadByStatus();
            if ($openStatementBatch):
                $openStatement = Genesis_Service_Statement::loadByAccountIdAndStatementBatchId(
                    $this->loggedUser->getAccount()->getAccountId(),
                    $openStatementBatch->getId()
                );
                if ($openStatement):
                    $urlAction = Genesis_Service_Feature::isActive(
                        AccountMgmt_Models_Features::NEW_STATEMENTS_PAGE,
                        ['account_id' => $accountId]
                    ) ? 'dispute' : 'view';
    ?>
                    <a class="item <?= $route === 'statement' && $action === $urlAction ? 'active yellow' : ''?>" data-page="statements" data-segment-category="sidebar" data-segment-label="statements" id="menu-statements" href="<?=$this->url(['action' => $urlAction, 'id' => $openStatement->getId()], 'statement')?>?account_id=<?=$accountId?>">
                        <i class="edit icon"></i> Reconcile <?php if ($urlAction === 'dispute'): ?><span stle="font-size:10px; float:inherit;" class="ui label green">BETA</span><?php endif; ?>
                    </a>
                <?php endif;
            endif;
        endif;
    ?>
    <?php
    if(Genesis_Service_Feature::isActive(AccountMgmt_Models_Features::CONTACTLESS_MOVEIN_BADGING, [])
        || $hasOmiCapableFms): ?>
        <a class="item <?= in_array($route, $moveinsArray) ? 'active yellow' : ''?>" data-page="moveins" data-segment-category="sidebar" data-segment-label="moveins" id="menu-moveins" href="<?=$this->url(['action'=>'contactless'], 'contactless-move-ins')?>">
            <img src="/images/ic_badge.svg" class="icon-badges">
            <span>Move-Ins</span>
        </a>
    <?php endif; ?>

    <?php if ($this->loggedUser->getAccount()->getCpa()): ?>
        <a class="item <?= $route === 'partners' ? 'active yellow' : ''?>" data-page="partners" data-segment-category="sidebar" data-segment-label="partners" id="menu-partners" href="<?=$this->url([], 'partners')?>">
            <img src="/images/partners_icon.svg" class="icon-badges">
            <span>Partners</span>
        </a>
    <?php endif; ?>

</div>
