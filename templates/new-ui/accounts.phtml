<?php if(isset($this->loggedUser) && $this->loggedUser->isMyfootGod()): ?>
<?php
    if (Genesis_Config_Server::isProduction()) $env = '';
    elseif (Genesis_Config_Server::isStaging()) $env = 'staging';
    elseif (Genesis_Config_Server::isDev()) $env = 'dev';
    else $env = 'local';
?>
<div class="ui search dropdown">
    <i class="dropdown icon"></i>
    <div class="default text"></div>
    <div class="menu">
    </div>
</div>

    <div id="preheader" class="preheader preheader-<?=$env?>"><span><?=ucfirst($env)?></span>

        <p id="accounts-header">
            <?=$this->loggedUser->getAccount()->getName()?> &mdash; <?=$this->loggedUser->getAccount()->getIntegrationsString()?> (<?=$this->loggedUser->getAccount()->getNumFacilities()?>)
            <?php if ($this->loggedUser->getAccount()->getStatus() && $this->loggedUser->getAccount()->getStatus() != "Live") { echo " &mdash; ".strtoupper($this->loggedUser->getAccount()->getStatus()); } ?>
            Account #<?=$this->loggedUser->getAccount()->getId();?>
            <?php if (($corps = $this->loggedUser->getAccount()->getCorporations()) !== false):
                foreach ($corps as $corp) {
                    echo 'Corp #'.$corp->getId();
                    break;
                }
            endif; ?>&nbsp;&nbsp;<i class="fa fa-chevron-right"></i>
            <?php
            $session = new Zend_Session_Namespace('default');
            if ($session->facilityId) { echo "Facility #".$session->facilityId; } ?>

        </p>

    </div>
            
    <style type="text/css">
        body {
            padding-top: 110px;
        }
    </style>
<?php endif; ?>
