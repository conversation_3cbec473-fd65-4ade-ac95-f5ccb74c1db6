{# Inventory Index Template #}

{% extends 'layout.html.twig' %}

{% block title %}{{ view.title }}{% endblock %}

{% block content %}
<script type="text/javascript">
    App.context = {
        facility_id: {{ (view.facility is defined and view.facility is not empty) ? view.facility.getId() : 'null' }}
    };
</script>

<div class="ui container">
    <div class="ui segment">
        <h1 class="ui header">
            <i class="warehouse icon"></i>
            <div class="content">
                Inventory Management
                <div class="sub header">Manage your facility's storage units and availability</div>
            </div>
        </h1>
    </div>

    {% if view.facility is defined and view.facility is not empty %}
        {% if not view.facility.getActive() %}
            <div id="inactive-facility-message" class="ui warning message" style="overflow:auto">
                <div class="header">
                    This facility is currently inactive and will not receive reservations on the SpareFoot network.
                    <button id="activate-facility" class="ui button compact primary pull-right">Activate Facility</button>
                </div>
            </div>  
        {% endif %}

        <div class="ui segment">
            <h3 class="ui header">{{ view.facility.getName() }}</h3>
            <p>Facility ID: {{ view.facility.getId() }}</p>
            
            <div class="ui divider"></div>
            
            <div class="ui stackable three column grid">
                <div class="column">
                    <div class="ui statistic">
                        <div class="value">
                            --
                        </div>
                        <div class="label">
                            Total Units
                        </div>
                    </div>
                </div>
                <div class="column">
                    <div class="ui statistic">
                        <div class="value">
                            --
                        </div>
                        <div class="label">
                            Available Units
                        </div>
                    </div>
                </div>
                <div class="column">
                    <div class="ui statistic">
                        <div class="value">
                            --
                        </div>
                        <div class="label">
                            Occupied Units
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="ui segment">
            <h3 class="ui header">
                <i class="list icon"></i>
                Unit Inventory
            </h3>
            <div class="ui message info">
                <i class="info circle icon"></i>
                Inventory management functionality is currently being developed. 
                Please contact support if you need immediate assistance with inventory management.
            </div>
            
            <div class="ui placeholder segment">
                <div class="ui icon header">
                    <i class="box icon"></i>
                    Inventory features coming soon
                </div>
                <div class="ui primary button">
                    <i class="plus icon"></i>
                    Add Unit (Coming Soon)
                </div>
            </div>
        </div>
    {% else %}
        <div class="ui warning message">
            <div class="header">No Facility Selected</div>
            <p>Please select a facility to manage inventory.</p>
        </div>
    {% endif %}
</div>

<div class="clear"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {# Include inventory-specific scripts #}
    {% for script in view.scripts %}
        <script src="{{ asset('js/' ~ script ~ '.js') }}"></script>
    {% endfor %}
{% endblock %}
